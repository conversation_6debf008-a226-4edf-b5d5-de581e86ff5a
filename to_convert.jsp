<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
<!------ システム共通ヘッダー  START ------>
	<jsp:include page="ptl000001_Header.jsp?PARAM=相場商品情報登録（SSN02002）"></jsp:include>
	<tr height="5"></tr>
<!------ システム共通ヘッダー  END ------>
<!------ Body START ------>
	<tr>
		<td align="center" valign="top">
<form name="MainForm" method="post" action="app">
<input type="hidden" name="ModifiedCondition" />
<input type="hidden" name="Modified" />
<input type="hidden" name="deleteLine" />
<input type="hidden" name="selectLine" />
<input type="hidden" name="line" />
<jsp:include page="rbs00000_common.jsp" flush="true" />
	<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
		<tr>
			<th>*取引先</th>
			<td nowrap>
			<%
			// 取引先権限振分
			if(RoleUtil.isTorihikisakiFurumai(role)){
			%>
				<input type="text" name="torihikisaki_cd" size="10" style="width: 52px;" value="<%=StringUtility.strIndisCvt(condition.getParameter("torihikisaki_cd"))%>" id="no_input_text" tabindex="-1" readonly />
				<input type="text" name="torihikisaki_na" size="40" style="width: 140px;" value="<%=StringUtility.strIndisCvt(condition.getParameter("torihikisaki_na"))%>" id="no_input_text" tabindex="-1" readonly />
	    	<%}else{%>
				<input type="text" name="torihikisaki_cd" size="8" style="width: 52px; ime-mode:disabled" maxlength="<%=torihikisakiCdLen %>" value="<%=StringUtility.strIndisCvt(condition.getParameter("torihikisaki_cd"))%>" />
				<input type="text" name="torihikisaki_na" size="26" style="width: 140px;" value="<%=StringUtility.strIndisCvt(condition.getParameter("torihikisaki_na"))%>" id="no_input_text" tabindex="-1" readonly />
                <img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd', 'MainForm.torihikisaki_na');" />
                <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd,MainForm.torihikisaki_na)" />
			<%}%>
			</td>
			<th>*納品日</th>
			<td nowrap>
				<input type="text" name="nohin_dt" size="10" style="width: 62px; ime-mode:disabled" value="<%=condition.getParameter("nohin_dt")%>" maxlength="8" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt);"/>
				<small>（YYYYMMDD）</small>
			</td>
		</tr>
		<tr>

			<th>*部門</th>

			<td nowrap>
				<input type="text" name="bunrui1_cd" maxlength="<%=bunrui1CdLen %>" size="8" style="width: 52px; ime-mode:disabled;" value="<%=StringUtility.strIndisCvt(condition.getParameter("bunrui1_cd"))%>" />
				<input type="text" name="bunrui1_na" id="no_input_text" style="width: 110px;" value="<%=StringUtility.strIndisCvt(condition.getParameter("bunrui1_na"))%>" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd','MainForm.bunrui1_na','MainForm.bunrui2_cd','MainForm.bunrui2_na','MainForm.bunrui5_cd','MainForm.bunrui5_na','1',MainForm.bunrui1_cd.value , '');"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui1_cd, MainForm.bunrui1_na);"/>
			</td>

			<th>大分類</th>

			<td nowrap>
				<input type="text" name="bunrui2_cd" size="8" style="width: 52px; ime-mode:disabled;" value="<%=StringUtility.strIndisCvt(condition.getParameter("bunrui2_cd"))%>" maxlength="<%=bunrui2CdLen %>" />
				<input type="text" name="bunrui2_na" style="width: 110px;" value="<%=StringUtility.strIndisCvt(condition.getParameter("bunrui2_na"))%>" id="no_input_text" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('','','MainForm.bunrui2_cd','MainForm.bunrui2_na','MainForm.bunrui5_cd','MainForm.bunrui5_na','2',MainForm.bunrui1_cd.value , MainForm.bunrui2_cd.value );"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui2_cd, MainForm.bunrui2_na);"/>
			</td>
		<tr>
			<th>小分類</th>
			<td nowrap>
				<input type="text" name="bunrui5_cd" maxlength="<%=bunrui5CdLen %>" size="8" style="width: 66px; ime-mode:disabled;" value="<%=StringUtility.strIndisCvt(condition.getParameter("bunrui5_cd"))%>" />
				<input type="text" name="bunrui5_na" id="no_input_text" style="width: 110px;" value="<%=StringUtility.strIndisCvt(condition.getParameter("bunrui5_na"))%>" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('','','','','MainForm.bunrui5_cd','MainForm.bunrui5_na','3',MainForm.bunrui1_cd.value , MainForm.bunrui2_cd.value );"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui5_cd, MainForm.bunrui5_na);"/>
			</td>
			<th>商品名(ｶﾅ)</th>
			<td nowrap>
				<input type="text" name="syohin_ka" maxlength="80" size="50" style="width: 262px;" value="<%=HTMLUtil.toText(condition.getParameter("syohin_ka"))%>" />
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.syohin_ka, MainForm.syohin_ka);"/>
			</td>
		</tr>
		<tr>
			<th>機能</th>
			<td nowrap>
				<label><input type="radio" name="func" value="<%=MarketFunctionDictionary.UPDATE.getCode()%>" <%=condition.getParameter("func") == null || MarketFunctionDictionary.UPDATE.getCode().equals(condition.getParameter("func")) ? "checked" : ""%> onClick="form.ModifiedCondition.value = '1'"/><span>登録</span></label>
				<label><input type="radio" name="func" value="<%=MarketFunctionDictionary.SEARCH.getCode()%>" <%=MarketFunctionDictionary.SEARCH.getCode().equals(condition.getParameter("func")) ? "checked" : ""%> onClick="form.ModifiedCondition.value = '1'"/><span>照会</span></label>
			</td>
			<th>受注対象</th>
			<td nowrap>
				<label><input type="radio" name="order" value="<%=OrderTargetDictionary.ALL.getCode()%>" <%=condition.getParameter("order") == null || OrderTargetDictionary.ALL.getCode().equals(condition.getParameter("order")) ? "checked" : ""%> onClick="form.ModifiedCondition.value = '1'"/>すべて</span></label>
				<label><input type="radio" name="order" value="<%=OrderTargetDictionary.TARGET.getCode()%>" <%=OrderTargetDictionary.TARGET.getCode().equals(condition.getParameter("order")) ? "checked" : ""%> onClick="form.ModifiedCondition.value = '1'"/><span>対象</span></label>
				<label><input type="radio" name="order" value="<%=OrderTargetDictionary.UNTARGET.getCode()%>" <%=OrderTargetDictionary.UNTARGET.getCode().equals(condition.getParameter("order")) ? "checked" : ""%> onClick="form.ModifiedCondition.value = '1'"/><span>対象外</span></label>
			</td>
		</tr>
	</table>
	<br />
	<input type="button" name="" value="&emsp;検&emsp;索&emsp;" class="btn" onClick="javascript:doSearchTran()"/>&nbsp
	<input type="button" name="" value="&emsp;追&emsp;加&emsp;" class="btn" onClick="javascript:doAddTran()"/>
	<input type="button" name="" value="&emsp;戻&emsp;る&emsp;" class="btn" onClick="javascript:isModifiedTran('marketSubMenu')"><br />
	<br />
	<br />
	<jsp:include page="InfoStringMdWare.jsp" />
	<br />
<%
	String func = HTMLUtil.toText(condition.getParameter("func"));
	boolean isFuncUpdate = true;
	if( func.equals(MarketFunctionDictionary.SEARCH.getCode()) ) {
		isFuncUpdate = false;
	}

	if(freshSyohinList != null && freshSyohinList.getMaxRows() > 0){
		List list = freshSyohinList.getBeanList();
%>
  <table>
		<tr>
		  <td width="40"></td><td align="left" nowrap><font color="0000FF">※変更内容は、納品日以降の全ての日付に反映されます。</font></td>
		<tr>
  </table>

	<table >
	<tr align="center">
	<td>

		<div align="left">
		<table border="0" cellspacing="1" cellpadding="0" class="data">
			<tr nowrap>
				<th nowrap style="width:45px" 	rowspan="3" align="center">商品<br>情報</th>
				<th nowrap style="width:85px" 	rowspan="3"	align="center">受注対象</th>
				<th nowrap style="width:45px" 	rowspan="3"	align="center">小売<br>発注<br>対象</th>
				<th nowrap style="width:45px" 	rowspan="3" align="center">対象<br>店舗</th>
				<th nowrap style="width:174" 	colspan="3"	align="center">商品コード</th>
				<th nowrap style="width:40px" 	rowspan="3"	align="center">発注<br>単位</th>
				<th nowrap style="width:45px" 	rowspan="3"	align="center">入数</th>
				<th nowrap style="width:90px" 	rowspan="3"	align="center">納品区分</th>
				<th nowrap style="width:80px"	rowspan="3"	align="center">経由<br>センター</th>
				<th nowrap style="width:25px" 	rowspan="3"	align="center">便</th>
				<th nowrap style="width:50px" 	rowspan="3" align="center">受納<br>カレンダ<br>対象外</th>
				<th nowrap style="width:40px" 	rowspan="3"	align="center">リード<br>タイム</th>
				<th nowrap style="width:80px" 	rowspan="3"	align="center">原単価</th>
				<%// 取引先は非表示
				if (RoleUtil.isTorihikisakiFurumai(role)) { %>
					<th nowrap style="width:71px" rowspan="3" align="center"></th>
				<%} else { %>
					<th nowrap style="width:71px" rowspan="3" align="center">出庫単価</th>
				<%} %>
				<th nowrap style="width:65px" 	rowspan="3"	align="center">売単価</th>
				<th nowrap style="width:45px" 	rowspan="3"></th>
				<th nowrap style="width:17px" 	rowspan="3"></th>
			</tr>
			<tr >
	            <th nowrap width="174" colspan="3" align="center">商品名</th>
			</tr>
			<tr >
	            <th nowrap width="58" align="center">産地</th>
			    <th nowrap width="58" align="center">等級</th>
			    <th nowrap width="58" align="center">規格</th>
			</tr>
		</table>
		</div>

		<div  align="left" style="overflow: scroll; overflow-x: hidden; height: 267px;">
		<table border="0" cellspacing="1" cellpadding="0" class="data">
	<%
			for(int i = 0; i < list.size(); i++){
				MarketFreshSyohinBean bean = (MarketFreshSyohinBean)list.get(i);
				String buturyuKb = HTMLUtil.toText(bean.getButuryuKb());
				String busyoKb = HTMLUtil.toText(bean.getKbBusyoKb());
	%>
			<tr>
				<input type="hidden" name="tokubai_kb_<%=i%>" value="<%=HTMLUtil.toText(bean.getTokubaiKb())%>" >
				<!------ 商品情報 ------>
				<td nowrap style="width: 45px; height: 56px;" rowspan="3" align="center"><input type="button" value="登録" style="width: 38px; height: 21px;" onClick="doCommentRegistTran('<%=i%>');"/></td>

				<!------ 受注対象 ------>
				<td id="02" class="string_label" width="81" nowrap rowspan="3" align="center">
				<select name="hachu_taisyo_kb_<%=i%>" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "disabled" : ""%> onChange="modifyHachuTaisyo('<%=i%>')" >
						<option value="<%= HTMLUtil.toText(HachuTaisyoKb.TAISYOGAI.getCode()) %>" onChange="changeModified();" <%= HachuTaisyoKb.TAISYOGAI.getCode().equals(bean.getHachuTaisyoKb().trim()) ? " selected" : "" %>><%= HachuTaisyoKb.TAISYOGAI.toString() %></option>
						<option value="<%= HTMLUtil.toText(HachuTaisyoKb.TOUJITSU_NOMI.getCode()) %>" onChange="changeModified();" <%= HachuTaisyoKb.TOUJITSU_NOMI.getCode().equals(bean.getHachuTaisyoKb().trim()) ? " selected" : "" %>><%= HachuTaisyoKb.TOUJITSU_NOMI.toString() %></option>
						<option value="<%= HTMLUtil.toText(HachuTaisyoKb.KEIZOKU.getCode()) %>" onChange="changeModified();" <%= HachuTaisyoKb.KEIZOKU.getCode().equals(bean.getHachuTaisyoKb().trim()) ? " selected" : "" %>><%= HachuTaisyoKb.KEIZOKU.toString() %></option>
				</select>
				</td>

				<!------ 小売発注対象 ------>
				<td nowrap width="45" rowspan="3" align="center">
					<%=HTMLUtil.toText((busyoKb.equals(BusyoKb.TAISYOGAI.getCode()) || busyoKb.equals(BusyoKb.TENPO_CANCEL.getCode())) ? "対象外" : "対象") %>
				<!------ 対象店舗 ------>
				<td nowrap width="45" rowspan="3" align="center"><input type="button" value="店舗" style="width: 38px; height: 21px;" onClick="doTenpoRegistTran('<%=i%>');"/></td>
				<!------ 商品コード ------>
				<td nowrap width="172" id="01" class="string_label" colspan="3" align="center">
					<input type="text" value="<%=HTMLUtil.toText(bean.getSyohinCd()) %>" size="29" style="width: 152px; border-width: 0px; " tabindex="-1" readonly />
				</td>
				<!------ 発注単位 ------>
	<%
				// 不定貫ならピンク色に変更
				String bgHachuTani = "";
				if( bean.getTeikanKb().equals(TeikanKb.FUTEIKAN.getCode()) ) {
					bgHachuTani = "background-color: #FFC8C8;";
				} else {
					bgHachuTani = "";
				}
	%>
				<td nowrap style="<%=bgHachuTani%>" id="01" class="string_label" width="36" rowspan="3" align="center">
					<input type="text" value="<%=HTMLUtil.toText(bean.getHachuTaniNa()) %>" size="4" style="<%=bgHachuTani%>width: 30px; border-width: 0px; " tabindex="-1" readonly />
				</td>

				<!------ 入数 ------>
				<input type="hidden" name="teikan_kb_<%=i%>" value="<%=HTMLUtil.toText(bean.getTeikanKb())%>" >
				<td id="02" nowrap width="45" rowspan="3" align="center"><input type="text" name="irisu_qt_<%=i%>" size="4" maxlength="4" value="<%=HTMLUtil.toText(bean.getIrisuQt())%>" class="numeric" style="width: 15px; ime-mode:disabled" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getTeikanKb().equals(TeikanKb.TEIKAN.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly" : ""%> /></td>

				<!------ 納品区分 ------>
				<td id="01" class="string_label" width="86" nowrap rowspan="3" align="center">
					<select name="buturyu_kb_<%=i%>" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "disabled" : ""%> onChange="modifyButuryu('<%=i%>')" >
	<%
		StringBuffer buturyuKbListBox = new StringBuffer();
		StringBuffer buturyuKb_hidden = new StringBuffer();
		boolean existFg = false;
		if(buturyuKbMap != null && buturyuKbMap.size() > 0){
			Iterator buturyuIte = buturyuKbMap.keySet().iterator();
			for(int j = 0; buturyuIte.hasNext(); j++){
				String key = buturyuIte.next().toString();

				buturyuKbListBox.append("<option value=\"").append(key).append("\"").append(key.equals(bean.getButuryuKb()) ? " selected" : "").append(">").append(HTMLUtil.toText((String)buturyuKbMap.get(key))).append("</option>\r\n");
				if (key.equals(bean.getButuryuKb())){
					existFg  = true;
				}
			}
		}
		if(!existFg){
			buturyuKbListBox.append("<option value=\"").append(bean.getButuryuKb()).append("\" selected> </option>\r\n");
		}
		out.print(buturyuKbListBox);
	%>
					</select>
	<%
		buturyuKb_hidden.append("<input type='hidden' name = 'hbuturyu_kb_").append(i).append("' value = '").append(bean.getButuryuKb()).append("'>");
		out.print(buturyuKb_hidden);
	%>

				</td>
				<!------ 経由センター ------>
				<td id="01" class="string_label" width="76" nowrap rowspan="3" align="center">
		 			<select name="center_cd_<%=i%>" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "disabled" : ""%> onChange="modifyKeiyuCenter('<%=i%>')" >
					<option value = ""></option>
	<%
		StringBuffer centerCdListBox = new StringBuffer();
		StringBuffer centerCd_hidden = new StringBuffer();


		String center = bean.getCenterCd();
		if (center == null) {
			center = "";
		} else {
			center = center.trim();
		}

		boolean centerExistFg = false;
		if(centerCdMap != null && centerCdMap.size() > 0){
			Iterator centerIte = centerCdMap.keySet().iterator();
			for(int j = 0; centerIte.hasNext(); j++){
				String key = centerIte.next().toString();

				centerCdListBox.append("<option value=\"").append(key).append("\"").append(key.equals(center) ? " selected" : "").append(">").append(HTMLUtil.toText((String)centerCdMap.get(key))).append("</option>\r\n");

				if (key.equals(center)){
					centerExistFg  = true;
				}

			}
		}
		if(!centerExistFg){

			if (!center.equals("")) {
				centerCdListBox.append("<option value=\"").append(center).append("\" selected> </option>\r\n");
			}
		}
		out.print(centerCdListBox);
	%>
					</select>
	<%
		centerCd_hidden.append("<input type='hidden' name = 'hcenter_cd_").append(i).append("' value = '").append(center).append("'>");
		out.print(centerCd_hidden);
	%>

				</td>
				<!------ 便 ------>
				<td id="05" nowrap width="25" rowspan="3" align="center"><input type="text" name="bin_kb_<%=i%>" size="" maxlength="1" value="<%=HTMLUtil.toText(bean.getBinKb())%>" class="numeric" style="width: 15px; ime-mode:disabled" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly" : ""%> /></td>
				<!------ 発納カレンダ対象外 ------>
				<td id="05" width="50" align="center" nowrap rowspan="3">
					<input name="hachuCalendarChk_<%=i%>" type="checkbox" <%=bean.getHachuCalendarOffFg().equals(HachuCalendarOffFg.TAISYO.getCode()) ? " checked" : ""%> onclick="hachuCalendarChk(MainForm.hachu_calendar_off_fg_<%=i%>)" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "tabindex=\"-1\" disabled " : ""%>/>
					<input name="hachu_calendar_off_fg_<%=i%>"  type="hidden" value="<%=bean.getHachuCalendarOffFg().equals(HachuCalendarOffFg.TAISYO.getCode()) ? "1" : "0"%>"/>
				</td>
				<!------ リードタイム ------>
				<td id="05" nowrap width="40" rowspan="3" align="center"><input type="text" name="leadtime_qt_<%=i%>" size="2" maxlength="2" value="<%=HTMLUtil.toText(bean.getLeadtimeQt())%>" class="numeric" style="width: 20px; ime-mode:disabled" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly" : ""%> /></td>
				<!------ 原単価 ------>
				<td id="01" nowrap width="80" rowspan="3" align="center"><input type="text" name="gentanka_vl_<%=i%>" size="11" maxlength="10" value="<%=HTMLUtil.toText(bean.getGentankaVlString(), "0.00")%>" class="numeric" style="width: 65px; ime-mode:disabled" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly" : ""%> /></td>
	<%
		String ikkatuShiireFg = bean.getIkkatuSiireDenpFg();
	%>
				<!------ 出庫単価 ------>
				<input type="hidden" name="ikkatu_siire_denp_fg_<%=i%>" value="<%= ikkatuShiireFg %>" >
				<td id="01" nowrap width="71" rowspan="3" align="center">
					<%
					// 取引先は非表示
					if(RoleUtil.isTorihikisakiFurumai(role)){
					%>
						<input type="hidden" name="syukotanka_vl_<%=i%>" value="<%=bean.getSyukotankaVl() == 0 ? "" : HTMLUtil.toText(bean.getSyukotankaVlString(), "0.00")%>" tabindex="-1" disabled readonly>
			    	<%}else{%>
						<input type="text" name="syukotanka_vl_<%=i%>" size="11" maxlength="10" value="<%=bean.getSyukotankaVl() == 0 ? "" : HTMLUtil.toText(bean.getSyukotankaVlString(), "0.00")%>" class="numeric" style="width: 65px; ime-mode:disabled" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) || ikkatuShiireFg.equals(IkkatuSiireDenpFg.TAISYOGAI.getCode()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly" : "" %> />
					<%}%>
				</td>
				<!------ 売単価 ------>
				<td id="01" width="65" rowspan="3" align="center"><input type="text" name="baitanka_vl_<%=i%>" size="8" maxlength="7" value="<%=bean.getBaitankaVlString()%>" class="numeric" style="width: 50px; ime-mode:disabled" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) || bean.getHachuTaisyoKb().equals(HachuTaisyoKb.TAISYOGAI.getCode()) || bean.getHonbuHenkoFg().equals(HonbuHenkoFg.ARI.getCode()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly" : ""%> /></td>
				<!------ 削除 ------>
				<td id="01" nowrap width="45" rowspan="3" align="center">
					<img src="./images/b_delete_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="javascript:doDeleteTran('<%=i%>')" <%=!isFuncUpdate || "1".equals(bean.getHachuZimeFg()) ? "disabled" : ""%>/>
				</td>
			</tr>
			<tr>
				<!------ 商品名 ------>
				<td nowrap width="170" id="01" class="string_label" colspan="3" align="center">
					<input type="text" value="<%=HTMLUtil.toText(bean.getSyohinNa()) %>" size="29" style="width: 155px; border-width: 0px; " tabindex="-1" readonly />
				</td>
			</tr>
			<tr>
				<!------ 産地 ------>
				<td id="01" class="string_label" nowrap width="54" align="center">
					<input type="text" value="<%=HTMLUtil.toText(bean.getSantiNa()) %>" size="7" style="width: 45px; border-width: 0px; " tabindex="-1" readonly />
				</td>
				<!------ 等階級 ------>
				<td id="01" class="string_label" nowrap width="54" align="center">
					<input type="text" value="<%=HTMLUtil.toText(bean.getTokaikyuNa()) %>" size="7" style="width: 45px; border-width: 0px; " tabindex="-1" readonly />
				</td>

				<!------ 規格 ------>
				<td id="01" class="string_label" nowrap width="54" align="center">
					<input type="text" value="<%=HTMLUtil.toText(bean.getKikakuNa()) %>" size="7" style="width: 45px; border-width: 0px; " tabindex="-1" readonly />
				</td>
			</tr>
	<%
			}
	%>
		  </table>
		  <div>
		</td>
		</tr>
      </table>


	<br />

	<div class="navi_data">
<%
	// 1ページあたりの表示件数の取得。
	int rowsInPage = 0;
	if( func.equals(MarketFunctionDictionary.SEARCH.getCode()) ) {
		rowsInPage = DtFreshSyohinSearch.ROWSINPAGE_SEARCH;
	} else {
		rowsInPage = DtFreshSyohinSearch.ROWSINPAGE_UPDATE;
	}

	String first = "";
	String prev = "";
	String next = "";
	String last = "";
	String onClick = "";

	if(freshSyohinList.getCurrentPageNumber() == 1){
		first = "disabled";
		prev = "disabled";
	}
	if(freshSyohinList.getCurrentPageNumber() == freshSyohinList.getLastPageNumber() ||
		freshSyohinList.getLastPageNumber() == 0
	){
		next = "disabled";
		last = "disabled";
	}

	if( isFuncUpdate == true ) {
		onClick = "javascript:doDecisionTran(";
	} else {
		onClick = "javascript:noUpdatePageChange(";
	}
%>
					<input type="button" style="width: 38px; height: 21px;" value="先頭" <%= first %> onClick="<%= onClick %>'first');">
					<input type="button" style="width: 86px; height: 21px;" value="前の<%= rowsInPage %>件" <%= prev %> onClick="<%= onClick %>'prev');">
					<input type="button" style="width: 86px; height: 21px;" value="次の<%= rowsInPage %>件" <%= next %> onClick="<%= onClick %>'next');">
					<input type="button" style="width: 38px; height: 21px;" value="最終" <%= last %> onClick="<%= onClick %>'last');">
	</div>
	<span class="size12">(<%=freshSyohinList.getMaxRows()%>件中<%=freshSyohinList.getStartRowInPage()%>～<%=freshSyohinList.getEndRowInPage()%>件目）</span>
	<br/><br/>
<%
	if(isFuncUpdate){
%>
	<input type="button" name="register" value="&emsp;登&emsp;録&emsp;" class="btn" onClick="doDecisionTran('')"/>
<%
	}
%>
	<input type="button" name="" value="&nbsp;&nbsp;&nbsp;Ｃ&nbsp;Ｓ&nbsp;Ｖ&nbsp;&nbsp;&nbsp;" class="btn" onClick="csvDownload();" />
<%
	}
%>
</form>
<!------ Body END ------>

<!---- システム共通フッター START ---->
<!---- システム共通フッター END ---->

</body>